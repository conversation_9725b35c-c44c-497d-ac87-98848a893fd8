{"rustc": 15497389221046826682, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 10369491684090452477, "path": 17770270771451006161, "deps": [[1009387600818341822, "matchers", false, 10104378011012525676], [1017461770342116999, "sharded_slab", false, 534873285806119379], [3722963349756955755, "once_cell", false, 5368440952414918665], [6048213226671835012, "smallvec", false, 8409635581287841494], [6981130804689348050, "tracing_serde", false, 7049954515537348057], [8606274917505247608, "tracing", false, 3370537206022343441], [8614575489689151157, "nu_ansi_term", false, 1443493351242393681], [9451456094439810778, "regex", false, 8698888870115434286], [9689903380558560274, "serde", false, 14334301300132040277], [10806489435541507125, "tracing_log", false, 6391735414269206981], [11033263105862272874, "tracing_core", false, 5997154125161353285], [12409575957772518135, "time", false, 3902591332851299545], [12427285511609802057, "thread_local", false, 3885851530227908746], [15367738274754116744, "serde_json", false, 13424706065444408846]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tracing-subscriber-e51a457777e6dd70/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}