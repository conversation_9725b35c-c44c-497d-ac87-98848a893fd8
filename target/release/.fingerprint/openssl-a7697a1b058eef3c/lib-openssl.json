{"rustc": 15497389221046826682, "features": "[\"default\", \"vendored\"]", "declared_features": "[\"aws-lc\", \"bindgen\", \"default\", \"unstable_boringssl\", \"v101\", \"v102\", \"v110\", \"v111\", \"vendored\"]", "target": 17474193825155910204, "profile": 2040997289075261528, "path": 2246932632676733693, "deps": [[2924422107542798392, "libc", false, 588411483917342838], [3722963349756955755, "once_cell", false, 5368440952414918665], [6635237767502169825, "foreign_types", false, 17382770554035971654], [7896293946984509699, "bitflags", false, 10601970671225538249], [8607891082156236373, "build_script_build", false, 354860981839683495], [9070360545695802481, "ffi", false, 9044492257258824178], [10099563100786658307, "openssl_macros", false, 7451767297840413950], [10411997081178400487, "cfg_if", false, 2348559036846732215]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/openssl-a7697a1b058eef3c/dep-lib-openssl", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}