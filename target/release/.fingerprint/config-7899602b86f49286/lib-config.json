{"rustc": 15497389221046826682, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 17342157952639649116, "path": 10162044332273838614, "deps": [[1213098572879462490, "json5_rs", false, 13764987915121637301], [1238778183371849706, "yaml_rust2", false, 10829832251628339210], [2244620803250265856, "ron", false, 15595609540373272980], [2356429411733741858, "ini", false, 11292051199851234935], [6517602928339163454, "path<PERSON><PERSON>", false, 3968249439002487000], [8786711029710048183, "toml", false, 8736745516318025892], [9689903380558560274, "serde", false, 14334301300132040277], [11946729385090170470, "async_trait", false, 11090996591112166339], [13475460906694513802, "convert_case", false, 10157082585902882273], [14718834678227948963, "winnow", false, 4956944179973760461], [15367738274754116744, "serde_json", false, 13424706065444408846]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/config-7899602b86f49286/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}