{"rustc": 15497389221046826682, "features": "[\"default\", \"prost\", \"prost-build\", \"transport\"]", "declared_features": "[\"cleanup-markdown\", \"default\", \"prost\", \"prost-build\", \"transport\"]", "target": 3882023571813807903, "profile": 1369601567987815722, "path": 11967964535478927124, "deps": [[99783594999256520, "prost_build", false, 17507060945901740677], [3060637413840920116, "proc_macro2", false, 13405385506261770010], [8549471757621926118, "prettyplease", false, 422511545433169039], [17990358020177143287, "quote", false, 13609315388968243395], [18149961000318489080, "syn", false, 1641094076836317208]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tonic-build-b2e6e4ee17ed1774/dep-lib-tonic_build", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}