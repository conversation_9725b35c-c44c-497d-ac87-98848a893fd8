{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 3165595516910038244, "profile": 2040997289075261528, "path": 14698983297451476235, "deps": [[784494742817713399, "tower_service", false, 13120832321508140532], [4405182208873388884, "http", false, 16415109884642029216], [7712452662827335977, "tower_layer", false, 7292375943021205383], [8915503303801890683, "http_body", false, 16624298123279707740], [9293824762099617471, "build_script_build", false, 8434925798402012455], [10229185211513642314, "mime", false, 6502389009036844849], [10629569228670356391, "futures_util", false, 9076510181596824301], [11946729385090170470, "async_trait", false, 11090996591112166339], [16066129441945555748, "bytes", false, 1944821580600278982]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/axum-core-bb5c4d064daa1249/dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}