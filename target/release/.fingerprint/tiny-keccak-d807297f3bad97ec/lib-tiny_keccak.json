{"rustc": 15497389221046826682, "features": "[\"default\", \"shake\"]", "declared_features": "[\"cshake\", \"default\", \"fips202\", \"k12\", \"keccak\", \"kmac\", \"parallel_hash\", \"sha3\", \"shake\", \"sp800\", \"tuple_hash\"]", "target": 8989851571439621957, "profile": 1369601567987815722, "path": 8572917723393887027, "deps": [[4280712380738690914, "build_script_build", false, 3494381530915170476], [10633447223404403777, "crunchy", false, 2887136928650292706]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tiny-keccak-d807297f3bad97ec/dep-lib-tiny_keccak", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}