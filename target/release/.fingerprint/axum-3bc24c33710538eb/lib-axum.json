{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"__private\", \"__private_docs\", \"default\", \"form\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 13920321295547257648, "profile": 11385615876995111466, "path": 17301405828534346599, "deps": [[40386456601120721, "percent_encoding", false, 6339746401739429549], [784494742817713399, "tower_service", false, 13120832321508140532], [1906322745568073236, "pin_project_lite", false, 13824975375445734951], [2517136641825875337, "sync_wrapper", false, 16248385799000013451], [3129130049864710036, "memchr", false, 15988951079447540251], [5695049318159433696, "tower", false, 5729608839109954856], [7695812897323945497, "itoa", false, 5131195845802881519], [7712452662827335977, "tower_layer", false, 7292375943021205383], [7858942147296547339, "rustversion", false, 40991473526179817], [8913795983780778928, "matchit", false, 2067533281460892108], [9010263965687315507, "http", false, 3674380776307342892], [9689903380558560274, "serde", false, 14334301300132040277], [10229185211513642314, "mime", false, 6502389009036844849], [10629569228670356391, "futures_util", false, 9076510181596824301], [14084095096285906100, "http_body", false, 4666279849996598278], [15176407853393882315, "axum_core", false, 5548638257473369735], [16066129441945555748, "bytes", false, 1944821580600278982], [16900715236047033623, "http_body_util", false, 516975375674898438]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/axum-3bc24c33710538eb/dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}