{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"headers\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 12074263998246110377, "profile": 2040997289075261528, "path": 2695021009374120339, "deps": [[40386456601120721, "percent_encoding", false, 6339746401739429549], [264090853244900308, "sync_wrapper", false, 15090883068933022067], [784494742817713399, "tower_service", false, 13120832321508140532], [1906322745568073236, "pin_project_lite", false, 13824975375445734951], [3129130049864710036, "memchr", false, 15988951079447540251], [3601586811267292532, "tower", false, 7715353000198732400], [4405182208873388884, "http", false, 16415109884642029216], [7414427314941361239, "hyper", false, 4168258056347974234], [7695812897323945497, "itoa", false, 5131195845802881519], [7712452662827335977, "tower_layer", false, 7292375943021205383], [8915503303801890683, "http_body", false, 16624298123279707740], [9293824762099617471, "axum_core", false, 11478324089716661737], [9678799920983747518, "matchit", false, 10966283103037570733], [9689903380558560274, "serde", false, 14334301300132040277], [10229185211513642314, "mime", false, 6502389009036844849], [10435729446543529114, "bitflags", false, 1784691539440697401], [10629569228670356391, "futures_util", false, 9076510181596824301], [11946729385090170470, "async_trait", false, 11090996591112166339], [16066129441945555748, "bytes", false, 1944821580600278982], [16244562316228021087, "build_script_build", false, 12638656247529032979]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/axum-c43624f1b65c1260/dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}