{"rustc": 15497389221046826682, "features": "[\"default\", \"prost\", \"prost-build\", \"transport\"]", "declared_features": "[\"cleanup-markdown\", \"default\", \"prost\", \"prost-build\", \"transport\"]", "target": 9025750215440372010, "profile": 9351476285418832939, "path": 7934114986830911633, "deps": [[2739579679802620019, "prost_build", false, 2453925664882441096], [3060637413840920116, "proc_macro2", false, 13405385506261770010], [8549471757621926118, "prettyplease", false, 422511545433169039], [16470553738848018267, "prost_types", false, 13584145770689800718], [17990358020177143287, "quote", false, 13609315388968243395], [18149961000318489080, "syn", false, 1641094076836317208]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tonic-build-3acda93b0cdad136/dep-lib-tonic_build", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}