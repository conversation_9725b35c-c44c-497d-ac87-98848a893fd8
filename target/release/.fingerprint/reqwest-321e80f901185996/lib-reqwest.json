{"rustc": 15497389221046826682, "features": "[\"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"deflate\", \"gzip\", \"hyper-rustls\", \"json\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-rustls\", \"tokio-util\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 2040997289075261528, "path": 12360358679216226155, "deps": [[40386456601120721, "percent_encoding", false, 6339746401739429549], [95042085696191081, "ipnet", false, 4740079376444248812], [126872836426101300, "async_compression", false, 10361838880751071391], [264090853244900308, "sync_wrapper", false, 15090883068933022067], [784494742817713399, "tower_service", false, 13120832321508140532], [1044435446100926395, "hyper_rustls", false, 1530864420401717408], [1288403060204016458, "tokio_util", false, 16176762386554293065], [1906322745568073236, "pin_project_lite", false, 13824975375445734951], [3150220818285335163, "url", false, 14928332036554555037], [3722963349756955755, "once_cell", false, 5368440952414918665], [4405182208873388884, "http", false, 16415109884642029216], [5986029879202738730, "log", false, 15448084732907268790], [7414427314941361239, "hyper", false, 4168258056347974234], [7620660491849607393, "futures_core", false, 1029861679584803060], [8915503303801890683, "http_body", false, 16624298123279707740], [9538054652646069845, "tokio", false, 12969563871196654092], [9689903380558560274, "serde", false, 14334301300132040277], [10229185211513642314, "mime", false, 6502389009036844849], [10629569228670356391, "futures_util", false, 9076510181596824301], [11107720164717273507, "system_configuration", false, 4447730288309391500], [11295624341523567602, "rustls", false, 5097617253206775094], [13809605890706463735, "h2", false, 10003386405540724437], [14564311161534545801, "encoding_rs", false, 10403533806233757200], [15367738274754116744, "serde_json", false, 13424706065444408846], [16066129441945555748, "bytes", false, 1944821580600278982], [16311359161338405624, "rustls_pemfile", false, 9680678441207267296], [16542808166767769916, "serde_urlencoded", false, 6411262989282864178], [16622232390123975175, "tokio_rustls", false, 1718852116594460059], [17652733826348741533, "webpki_roots", false, 2766041286891874008], [18066890886671768183, "base64", false, 15151799892251982339]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/reqwest-321e80f901185996/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}