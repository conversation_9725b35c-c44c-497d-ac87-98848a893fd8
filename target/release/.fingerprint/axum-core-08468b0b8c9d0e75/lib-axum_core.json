{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 11385615876995111466, "path": 12675119491120528943, "deps": [[784494742817713399, "tower_service", false, 13120832321508140532], [1906322745568073236, "pin_project_lite", false, 13824975375445734951], [2517136641825875337, "sync_wrapper", false, 16248385799000013451], [7620660491849607393, "futures_core", false, 1029861679584803060], [7712452662827335977, "tower_layer", false, 7292375943021205383], [7858942147296547339, "rustversion", false, 40991473526179817], [9010263965687315507, "http", false, 3674380776307342892], [10229185211513642314, "mime", false, 6502389009036844849], [14084095096285906100, "http_body", false, 4666279849996598278], [16066129441945555748, "bytes", false, 1944821580600278982], [16900715236047033623, "http_body_util", false, 516975375674898438]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/axum-core-08468b0b8c9d0e75/dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}